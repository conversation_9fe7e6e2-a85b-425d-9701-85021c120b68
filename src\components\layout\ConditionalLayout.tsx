'use client';

import { usePathname } from 'next/navigation';
import { useState, useEffect } from 'react';
import { useSidebar } from '@/lib/sidebar-context';
import ClientSidebar from '@/components/layout/ClientSidebar';
import Footer from '@/components/layout/Footer';
import RealisticSunLight from '@/components/ui/RealisticSunLight';
import ContentIllumination from '@/components/ui/ContentIllumination';
import ErrorBoundary from '@/components/ui/ErrorBoundary';
import { cn } from '@/lib/utils';

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

export default function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname();
  const isAdminRoute = pathname?.startsWith('/admin');
  const { isCollapsed, isMobileOpen } = useSidebar();
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [windowWidth, setWindowWidth] = useState<number>(0);
  const [mounted, setMounted] = useState(false);

  // Track window width for responsive calculations
  useEffect(() => {
    setMounted(true);

    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    // Set initial width
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Touch gesture handlers for main content area
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!touchStart) return;

    const touchEnd = e.changedTouches[0].clientX;
    const distance = touchStart - touchEnd;
    const isRightSwipe = distance < -50;

    // Only trigger on right swipe from left edge on mobile
    if (window.innerWidth < 768 && touchStart < 50 && isRightSwipe) {
      // Dispatch custom event to open sidebar
      window.dispatchEvent(new CustomEvent('openMobileSidebar'));
    }
  };

  if (isAdminRoute) {
    // Admin layout - no main site navigation/footer
    return (
      <ErrorBoundary>
        <div className="min-h-screen bg-background-light dark:bg-background-dark text-text-primary-light dark:text-text-primary-dark">
          {children}
        </div>
      </ErrorBoundary>
    );
  }

  // Calculate dynamic margin based on screen size and sidebar state - Updated for optimized widths
  const getMainContentMargin = () => {
    if (!mounted || windowWidth === 0) return '0px'; // SSR/initial render

    if (windowWidth < 768) {
      // Mobile: no margin
      return '0px';
    } else if (windowWidth < 1024) {
      // Tablet: always collapsed width
      return '90px';
    } else {
      // Desktop: responsive to collapse state
      return isCollapsed ? '90px' : '220px';
    }
  };

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <div className="min-h-screen bg-background-light dark:bg-background-dark text-text-primary-light dark:text-text-primary-dark opacity-0">
        {children}
      </div>
    );
  }

  // Main site layout - with sidebar navigation and footer
  return (
    <ErrorBoundary>
      <div
        className="min-h-screen bg-background-light dark:bg-background-dark text-text-primary-light dark:text-text-primary-dark"
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
      >
        <ErrorBoundary fallback={<div className="p-4 text-red-500">Navigation error occurred</div>}>
          <ClientSidebar />
        </ErrorBoundary>

        <ErrorBoundary fallback={<div className="hidden">Theme toggle error</div>}>
          <RealisticSunLight />
        </ErrorBoundary>

        <ErrorBoundary fallback={<div className="hidden">Lighting effect error</div>}>
          <ContentIllumination />
        </ErrorBoundary>

        <div
          className={cn(
            "sidebar-transition",
            // Base mobile styles
            "ml-0",
            // Tablet styles - always collapsed (updated width)
            "md:ml-[90px]",
            // Desktop styles - responsive to collapse state (updated widths)
            isCollapsed ? "lg:ml-[90px]" : "lg:ml-[220px]"
          )}
          style={{
            marginLeft: getMainContentMargin()
          }}
        >
          <main
            id="main-content"
            className="min-h-screen px-4 sm:px-6 md:px-8 lg:px-12 py-6 md:py-8 pt-16 md:pt-8"
            role="main"
            aria-label="Main content"
          >
            <div className="max-w-7xl mx-auto">
              <ErrorBoundary>
                {children}
              </ErrorBoundary>
            </div>
          </main>

          <ErrorBoundary fallback={<div className="p-4 text-center text-gray-500">Footer unavailable</div>}>
            <Footer />
          </ErrorBoundary>
        </div>
      </div>
    </ErrorBoundary>
  );
}
