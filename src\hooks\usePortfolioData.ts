'use client';

import { useState, useEffect, useCallback } from 'react';

interface PortfolioData {
  personal: any;
  skills: any[];
  projects: any[];
  education: any[];
  experience: any[];
  seo: any;
}

interface UsePortfolioDataReturn {
  data: PortfolioData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Global cache to prevent multiple API calls
let globalCache: {
  data: PortfolioData | null;
  timestamp: number;
  promise: Promise<PortfolioData> | null;
} = {
  data: null,
  timestamp: 0,
  promise: null,
};

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export function usePortfolioData(): UsePortfolioDataReturn {
  const [data, setData] = useState<PortfolioData | null>(globalCache.data);
  const [loading, setLoading] = useState(!globalCache.data);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async (): Promise<PortfolioData> => {
    try {
      const response = await fetch('/api/portfolio');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      
      // Ensure all required fields exist with defaults
      const portfolioData: PortfolioData = {
        personal: result.personal || {},
        skills: result.skills || [],
        projects: result.projects || [],
        education: result.education || [],
        experience: result.experience || [],
        seo: result.seo || {},
      };

      return portfolioData;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch portfolio data';
      throw new Error(errorMessage);
    }
  }, []);

  const refetch = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Clear cache to force fresh data
      globalCache.data = null;
      globalCache.timestamp = 0;
      globalCache.promise = null;
      
      const result = await fetchData();
      
      // Update global cache
      globalCache.data = result;
      globalCache.timestamp = Date.now();
      globalCache.promise = null;
      
      setData(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch portfolio data';
      setError(errorMessage);
      console.error('Error fetching portfolio data:', err);
    } finally {
      setLoading(false);
    }
  }, [fetchData]);

  useEffect(() => {
    const loadData = async () => {
      // Check if we have fresh cached data
      const now = Date.now();
      const isCacheValid = globalCache.data && (now - globalCache.timestamp) < CACHE_DURATION;
      
      if (isCacheValid) {
        setData(globalCache.data);
        setLoading(false);
        return;
      }

      // Check if there's already a request in progress
      if (globalCache.promise) {
        try {
          const result = await globalCache.promise;
          setData(result);
          setLoading(false);
          return;
        } catch (err) {
          // If the existing promise failed, we'll make a new request
          globalCache.promise = null;
        }
      }

      // Make a new request
      setLoading(true);
      setError(null);
      
      try {
        // Store the promise to prevent duplicate requests
        globalCache.promise = fetchData();
        const result = await globalCache.promise;
        
        // Update global cache
        globalCache.data = result;
        globalCache.timestamp = now;
        globalCache.promise = null;
        
        setData(result);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch portfolio data';
        setError(errorMessage);
        console.error('Error fetching portfolio data:', err);
        
        // Clear the failed promise
        globalCache.promise = null;
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch,
  };
}

// Hook for accessing specific sections of portfolio data
export function usePersonalInfo() {
  const { data, loading, error, refetch } = usePortfolioData();
  return {
    personalInfo: data?.personal || null,
    loading,
    error,
    refetch,
  };
}

export function useSkills() {
  const { data, loading, error, refetch } = usePortfolioData();
  return {
    skills: data?.skills || [],
    loading,
    error,
    refetch,
  };
}

export function useProjects() {
  const { data, loading, error, refetch } = usePortfolioData();
  return {
    projects: data?.projects || [],
    loading,
    error,
    refetch,
  };
}

export function useEducation() {
  const { data, loading, error, refetch } = usePortfolioData();
  return {
    education: data?.education || [],
    loading,
    error,
    refetch,
  };
}

export function useExperience() {
  const { data, loading, error, refetch } = usePortfolioData();
  return {
    experience: data?.experience || [],
    loading,
    error,
    refetch,
  };
}

export function useSeoConfig() {
  const { data, loading, error, refetch } = usePortfolioData();
  return {
    seoConfig: data?.seo || null,
    loading,
    error,
    refetch,
  };
}
