'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Mail, Phone, MapPin, Send, Github, Linkedin, MessageCircle, Clock, CheckCircle } from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Toast from '@/components/ui/Toast';

const contactSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
});

type ContactForm = z.infer<typeof contactSchema>;

const socialIcons = {
  github: Github,
  linkedin: Linkedin,
  mail: Mail,
};

export default function ContactPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [personalInfo, setPersonalInfo] = useState<any>({});
  const [loading, setLoading] = useState(true);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ContactForm>({
    resolver: zodResolver(contactSchema),
  });

  useEffect(() => {
    const fetchPersonalInfo = async () => {
      try {
        const response = await fetch('/api/portfolio');
        if (response.ok) {
          const data = await response.json();
          setPersonalInfo(data.personal || {});
        }
      } catch (error) {
        console.error('Failed to fetch personal info:', error);
        setPersonalInfo({});
      } finally {
        setLoading(false);
      }
    };

    fetchPersonalInfo();
  }, []);

  const onSubmit = async (data: ContactForm) => {
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        setIsSubmitted(true);
        reset();
      } else {
        const errorData = await response.json();
        console.error('Form submission error:', errorData.error);
        // You could add error state handling here
      }
    } catch (error) {
      console.error('Network error:', error);
      // You could add error state handling here
    } finally {
      setIsSubmitting(false);
    }
    
    // Reset success message after 5 seconds
    setTimeout(() => setIsSubmitted(false), 5000);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <div className="min-h-screen py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-bold font-display gradient-text mb-6">
            Get In Touch
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Have a project in mind or just want to chat? I'd love to hear from you!
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 lg:grid-cols-3 gap-8"
        >
          {/* Contact Information */}
          <motion.div variants={itemVariants} className="lg:col-span-1">
            <div className="space-y-6">
              {/* Contact Details */}
              <Card>
                <Card.Header>
                  <Card.Title>Contact Information</Card.Title>
                  <Card.Description>
                    Feel free to reach out through any of these channels
                  </Card.Description>
                </Card.Header>
                <Card.Content>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <div className="p-2 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex-shrink-0">
                        <Mail className="text-primary-500 dark:text-primary-400" size={20} />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 dark:text-white mb-1">Email</p>
                        <a
                          href={`mailto:${personalInfo.email || '<EMAIL>'}`}
                          className="text-primary-500 dark:text-primary-400 hover:underline break-all text-sm sm:text-base"
                          title={personalInfo.email || '<EMAIL>'}
                        >
                          {personalInfo.email || '<EMAIL>'}
                        </a>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="p-2 bg-accent-100 dark:bg-accent-900/30 rounded-lg flex-shrink-0">
                        <MapPin className="text-accent-500 dark:text-accent-400" size={20} />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 dark:text-white mb-1">Location</p>
                        <p className="text-gray-600 dark:text-gray-400 text-sm sm:text-base break-words">
                          {personalInfo.location || 'Remote'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg flex-shrink-0">
                        <Clock className="text-green-500 dark:text-green-400" size={20} />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 dark:text-white mb-1">Response Time</p>
                        <p className="text-gray-600 dark:text-gray-400 text-sm sm:text-base">
                          Usually within 24 hours
                        </p>
                      </div>
                    </div>
                  </div>
                </Card.Content>
              </Card>

              {/* Social Links */}
              <Card>
                <Card.Header>
                  <Card.Title>Connect With Me</Card.Title>
                  <Card.Description>
                    Let's connect on social media
                  </Card.Description>
                </Card.Header>
                <Card.Content>
                  <div className="flex space-x-4">
                    {(personalInfo.socialLinks || []).map((social) => {
                      const IconComponent = socialIcons[social.icon as keyof typeof socialIcons];
                      if (!IconComponent) return null;

                      return (
                        <motion.a
                          key={social.name}
                          href={social.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          whileHover={{ scale: 1.1, y: -2 }}
                          whileTap={{ scale: 0.95 }}
                          className="p-3 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-white transition-all duration-200 shadow-lg hover:shadow-xl"
                          style={{
                            '--hover-color': social.color,
                          } as React.CSSProperties}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = social.color || '#3B82F6';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = '';
                          }}
                          aria-label={`Visit ${social.name}`}
                        >
                          <IconComponent size={24} />
                        </motion.a>
                      );
                    })}
                  </div>
                </Card.Content>
              </Card>

              {/* Quick Message */}
              <Card className="bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20">
                <Card.Content>
                  <div className="flex items-start space-x-3">
                    <MessageCircle className="text-primary-500 dark:text-primary-400 mt-1" size={20} />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white mb-2">
                        Quick Response
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        For urgent inquiries, feel free to reach out directly via email or LinkedIn. 
                        I typically respond within a few hours during business days.
                      </p>
                    </div>
                  </div>
                </Card.Content>
              </Card>
            </div>
          </motion.div>

          {/* Contact Form */}
          <motion.div variants={itemVariants} className="lg:col-span-2">
            <Card>
              <Card.Header>
                <Card.Title>Send Me a Message</Card.Title>
                <Card.Description>
                  Fill out the form below and I'll get back to you as soon as possible
                </Card.Description>
              </Card.Header>
              <Card.Content>
                {/* Success message inside form */}
                {isSubmitted && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mb-6 p-4 bg-green-100 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-lg relative z-notification"
                  >
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="text-green-500" size={20} />
                      <p className="text-green-700 dark:text-green-400 font-medium">
                        Message sent successfully! I'll get back to you soon.
                      </p>
                    </div>
                  </motion.div>
                )}

                {/* Toast notification using the new Toast component */}
                <Toast
                  type="success"
                  message="Message sent successfully! I'll get back to you soon."
                  isVisible={isSubmitted}
                  onDismiss={() => setIsSubmitted(false)}
                  duration={5000}
                  position="top-center"
                />

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Name *
                      </label>
                      <input
                        {...register('name')}
                        type="text"
                        id="name"
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                        placeholder="Your full name"
                      />
                      {errors.name && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400 relative z-10">{errors.name.message}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Email *
                      </label>
                      <input
                        {...register('email')}
                        type="email"
                        id="email"
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                        placeholder="<EMAIL>"
                      />
                      {errors.email && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400 relative z-10">{errors.email.message}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Subject *
                    </label>
                    <input
                      {...register('subject')}
                      type="text"
                      id="subject"
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                      placeholder="What's this about?"
                    />
                    {errors.subject && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400 relative z-10">{errors.subject.message}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Message *
                    </label>
                    <textarea
                      {...register('message')}
                      id="message"
                      rows={6}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors resize-none"
                      placeholder="Tell me about your project or just say hello!"
                    />
                    {errors.message && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400 relative z-10">{errors.message.message}</p>
                    )}
                  </div>

                  <Button
                    type="submit"
                    loading={isSubmitting}
                    icon={<Send size={20} />}
                    size="lg"
                    fullWidth
                  >
                    {isSubmitting ? 'Sending...' : 'Send Message'}
                  </Button>
                </form>
              </Card.Content>
            </Card>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}
