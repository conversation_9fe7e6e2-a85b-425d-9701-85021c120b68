'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Activity, X, AlertTriangle, CheckCircle, Clock } from 'lucide-react';
import { usePerformanceMonitor } from '@/lib/performance-monitor';

// Only show in development
const isDevelopment = process.env.NODE_ENV === 'development';

export default function PerformanceDebugger() {
  const [isOpen, setIsOpen] = useState(false);
  const [summary, setSummary] = useState<any>(null);
  const [issues, setIssues] = useState<string[]>([]);
  const { getSummary, getIssues } = usePerformanceMonitor();

  useEffect(() => {
    if (!isDevelopment) return;

    const updateMetrics = () => {
      setSummary(getSummary());
      setIssues(getIssues());
    };

    // Update immediately
    updateMetrics();

    // Update every 5 seconds
    const interval = setInterval(updateMetrics, 5000);

    return () => clearInterval(interval);
  }, [getSummary, getIssues]);

  if (!isDevelopment) return null;

  return (
    <>
      {/* Toggle Button */}
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className={`fixed bottom-4 right-4 z-[9999] p-3 rounded-full shadow-lg transition-colors ${
          issues.length > 0 
            ? 'bg-red-500 hover:bg-red-600 text-white' 
            : 'bg-blue-500 hover:bg-blue-600 text-white'
        }`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title={`Performance Monitor ${issues.length > 0 ? `(${issues.length} issues)` : ''}`}
      >
        <Activity size={20} />
        {issues.length > 0 && (
          <motion.div
            className="absolute -top-1 -right-1 w-5 h-5 bg-red-600 rounded-full flex items-center justify-center text-xs font-bold"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: 'spring', stiffness: 500, damping: 30 }}
          >
            {issues.length}
          </motion.div>
        )}
      </motion.button>

      {/* Debug Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 100 }}
            className="fixed bottom-20 right-4 w-96 max-h-96 bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 z-[9998] overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-2">
                <Activity size={16} className="text-blue-500" />
                <h3 className="font-semibold text-gray-900 dark:text-white">
                  Performance Monitor
                </h3>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
              >
                <X size={16} className="text-gray-500" />
              </button>
            </div>

            {/* Content */}
            <div className="p-4 max-h-80 overflow-y-auto">
              {/* Issues Section */}
              {issues.length > 0 && (
                <div className="mb-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <AlertTriangle size={16} className="text-red-500" />
                    <h4 className="font-medium text-red-700 dark:text-red-400">
                      Performance Issues ({issues.length})
                    </h4>
                  </div>
                  <div className="space-y-2">
                    {issues.map((issue, index) => (
                      <div
                        key={index}
                        className="p-2 bg-red-50 dark:bg-red-900/20 rounded text-sm text-red-700 dark:text-red-300"
                      >
                        {issue}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Summary Section */}
              {summary && (
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <CheckCircle size={16} className="text-green-500" />
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      Performance Summary
                    </h4>
                  </div>

                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded">
                      <div className="text-gray-600 dark:text-gray-400">API Calls</div>
                      <div className="font-semibold text-gray-900 dark:text-white">
                        {summary.recentApiCalls}
                      </div>
                    </div>

                    <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded">
                      <div className="text-gray-600 dark:text-gray-400">Avg API Time</div>
                      <div className="font-semibold text-gray-900 dark:text-white">
                        {Math.round(summary.averageApiTime)}ms
                      </div>
                    </div>

                    <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded">
                      <div className="text-gray-600 dark:text-gray-400">Total Metrics</div>
                      <div className="font-semibold text-gray-900 dark:text-white">
                        {summary.totalMetrics}
                      </div>
                    </div>

                    <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded">
                      <div className="text-gray-600 dark:text-gray-400">Status</div>
                      <div className={`font-semibold ${
                        issues.length === 0 
                          ? 'text-green-600 dark:text-green-400' 
                          : 'text-red-600 dark:text-red-400'
                      }`}>
                        {issues.length === 0 ? 'Good' : 'Issues'}
                      </div>
                    </div>
                  </div>

                  {/* Slowest Component */}
                  {summary.slowestComponent && (
                    <div className="p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded">
                      <div className="flex items-center space-x-2 mb-1">
                        <Clock size={14} className="text-yellow-600" />
                        <span className="text-sm font-medium text-yellow-700 dark:text-yellow-400">
                          Slowest Component
                        </span>
                      </div>
                      <div className="text-sm text-yellow-600 dark:text-yellow-300">
                        {summary.slowestComponent.name.replace('component-render-', '')}: {' '}
                        {Math.round(summary.slowestComponent.value)}ms
                      </div>
                    </div>
                  )}

                  {/* Recent Metrics */}
                  {summary.metrics && summary.metrics.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Recent Activity
                      </h5>
                      <div className="space-y-1 max-h-32 overflow-y-auto">
                        {summary.metrics.slice(-5).map((metric: any, index: number) => (
                          <div
                            key={index}
                            className="text-xs text-gray-600 dark:text-gray-400 flex justify-between"
                          >
                            <span className="truncate">
                              {metric.name.replace(/^(api-call-|component-render-)/, '')}
                            </span>
                            <span>
                              {metric.type === 'timing' ? `${Math.round(metric.value)}ms` : metric.value}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Tips */}
              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
                <h5 className="text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">
                  💡 Performance Tips
                </h5>
                <ul className="text-xs text-blue-600 dark:text-blue-300 space-y-1">
                  <li>• API calls should be &lt;5 per page load</li>
                  <li>• Component renders should be &lt;100ms</li>
                  <li>• Use React.memo for expensive components</li>
                  <li>• Implement proper loading states</li>
                </ul>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
