import fs from 'fs/promises';
import path from 'path';

const DATA_DIR = path.join(process.cwd(), 'data');
const PORTFOLIO_FILE = path.join(DATA_DIR, 'portfolio.json');
const CONTACTS_FILE = path.join(DATA_DIR, 'contacts.json');

// Ensure data directory exists
async function ensureDataDir() {
  try {
    await fs.access(DATA_DIR);
  } catch {
    await fs.mkdir(DATA_DIR, { recursive: true });
  }
}

// Portfolio Data Management
export async function getPortfolioData() {
  try {
    await ensureDataDir();
    const data = await fs.readFile(PORTFOLIO_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    // If file doesn't exist, return default data and create the file
    const defaultData = {
      personal: {
        name: "<PERSON><PERSON><PERSON>",
        title: "Software Engineer",
        tagline: "Crafting scalable, elegant solutions with code",
        bio: "Passionate software engineer with a fresh perspective on modern development. I love building user-centric applications that solve real-world problems through clean, efficient code and thoughtful design.",
        location: "Samsun, Turkey",
        email: "<EMAIL>",
        resumeUrl: "/resume.pdf",
        socialLinks: [
          {
            name: "GitHub",
            url: "https://github.com/ehsan-amini",
            icon: "github",
            color: "#333"
          },
          {
            name: "LinkedIn",
            url: "https://linkedin.com/in/ehsan-amini",
            icon: "linkedin",
            color: "#0077B5"
          },
          {
            name: "Email",
            url: "mailto:<EMAIL>",
            icon: "mail",
            color: "#EA4335"
          }
        ]
      },
      skills: [],
      projects: [],
      education: [],
      experience: [],
      seo: {
        title: "Ehsan Amini - Software Engineer Portfolio",
        description: "Software Engineer specializing in modern web development. Crafting scalable, elegant solutions with React, Node.js, and Python.",
        keywords: ["Software Engineer", "Web Developer", "React", "Node.js", "Python", "Full Stack Developer", "Ehsan Amini"],
        author: "Ehsan Amini",
        siteUrl: "https://ehsan-amini.vercel.app",
        image: "/og-image.jpg"
      }
    };
    await savePortfolioData(defaultData);
    return defaultData;
  }
}

export async function savePortfolioData(data: any) {
  try {
    await ensureDataDir();
    await fs.writeFile(PORTFOLIO_FILE, JSON.stringify(data, null, 2));
    return { success: true };
  } catch (error) {
    console.error('Error saving portfolio data:', error);
    return { success: false, error: 'Failed to save data' };
  }
}

// Contact Messages Management
export interface ContactMessage {
  id: string;
  name: string;
  email: string;
  subject: string;
  message: string;
  createdAt: string;
  read: boolean;
}

export async function getContactMessages(): Promise<ContactMessage[]> {
  try {
    await ensureDataDir();
    const data = await fs.readFile(CONTACTS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    // If file doesn't exist, return empty array
    return [];
  }
}

export async function saveContactMessage(message: Omit<ContactMessage, 'id' | 'createdAt' | 'read'>) {
  try {
    const messages = await getContactMessages();
    const newMessage: ContactMessage = {
      ...message,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      read: false
    };

    messages.unshift(newMessage); // Add to beginning

    await ensureDataDir();
    await fs.writeFile(CONTACTS_FILE, JSON.stringify(messages, null, 2));
    return { success: true, message: newMessage };
  } catch (error) {
    console.error('Error saving contact message:', error);
    return { success: false, error: 'Failed to save message' };
  }
}

export async function addContactMessage(message: any) {
  await ensureDataDir();
  const messages = await getContactMessages();
  messages.unshift(message); // Add to beginning of array
  await fs.writeFile(CONTACTS_FILE, JSON.stringify(messages, null, 2));
}

export async function markMessageAsRead(messageId: string) {
  try {
    const messages = await getContactMessages();
    const messageIndex = messages.findIndex(m => m.id === messageId);
    
    if (messageIndex === -1) {
      return { success: false, error: 'Message not found' };
    }
    
    messages[messageIndex].read = true;
    
    await fs.writeFile(CONTACTS_FILE, JSON.stringify(messages, null, 2));
    return { success: true };
  } catch (error) {
    console.error('Error marking message as read:', error);
    return { success: false, error: 'Failed to update message' };
  }
}

export async function deleteContactMessage(messageId: string) {
  try {
    const messages = await getContactMessages();
    const filteredMessages = messages.filter(m => m.id !== messageId);
    
    await fs.writeFile(CONTACTS_FILE, JSON.stringify(filteredMessages, null, 2));
    return { success: true };
  } catch (error) {
    console.error('Error deleting message:', error);
    return { success: false, error: 'Failed to delete message' };
  }
}

// Project Management
export async function addProject(project: any) {
  try {
    const data = await getPortfolioData();
    const newProject = {
      ...project,
      id: Date.now().toString(),
      createdAt: new Date().toISOString()
    };
    
    data.projects.unshift(newProject);
    await savePortfolioData(data);
    return { success: true, project: newProject };
  } catch (error) {
    console.error('Error adding project:', error);
    return { success: false, error: 'Failed to add project' };
  }
}

export async function updateProject(projectId: string, updates: any) {
  try {
    const data = await getPortfolioData();
    const projectIndex = data.projects.findIndex((p: any) => p.id === projectId);
    
    if (projectIndex === -1) {
      return { success: false, error: 'Project not found' };
    }
    
    data.projects[projectIndex] = {
      ...data.projects[projectIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    await savePortfolioData(data);
    return { success: true, project: data.projects[projectIndex] };
  } catch (error) {
    console.error('Error updating project:', error);
    return { success: false, error: 'Failed to update project' };
  }
}

export async function deleteProject(projectId: string) {
  try {
    const data = await getPortfolioData();
    data.projects = data.projects.filter((p: any) => p.id !== projectId);
    
    await savePortfolioData(data);
    return { success: true };
  } catch (error) {
    console.error('Error deleting project:', error);
    return { success: false, error: 'Failed to delete project' };
  }
}

// About Section Management
export async function updateAboutSection(section: string, updates: any) {
  try {
    const data = await getPortfolioData();
    
    if (section === 'personal') {
      data.personal = { ...data.personal, ...updates };
    } else if (section === 'skills') {
      data.skills = updates;
    } else if (section === 'experience') {
      data.experience = updates;
    } else if (section === 'education') {
      data.education = updates;
    } else {
      return { success: false, error: 'Invalid section' };
    }
    
    await savePortfolioData(data);
    return { success: true, data: data[section] };
  } catch (error) {
    console.error('Error updating about section:', error);
    return { success: false, error: 'Failed to update section' };
  }
}
