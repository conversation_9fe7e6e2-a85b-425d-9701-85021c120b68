export interface Project {
  id: string;
  title: string;
  description: string;
  longDescription?: string;
  technologies: string[];
  category: 'frontend' | 'backend' | 'fullstack' | 'mobile' | 'other';
  image: string;
  githubUrl?: string;
  liveUrl?: string;
  featured: boolean;
  year: number;
  status: 'completed' | 'in-progress' | 'planned';
}

export interface Skill {
  name: string;
  category: 'frontend' | 'backend' | 'database' | 'tools' | 'languages' | 'other';
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  icon?: string;
  color?: string;
}

export interface Experience {
  id: string;
  company: string;
  position: string;
  duration: string;
  description: string[];
  technologies: string[];
  type: 'work' | 'internship' | 'freelance' | 'project';
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  duration: string;
  gpa?: string;
  achievements?: string[];
}

export interface ContactForm {
  name: string;
  email: string;
  subject?: string;
  message: string;
}

export interface SocialLink {
  name: string;
  url: string;
  icon: string;
  color?: string;
}

export interface PersonalInfo {
  name: string;
  title: string;
  tagline: string;
  bio: string;
  location: string;
  email: string;
  phone?: string;
  resumeUrl: string;
  profileImage?: string;
  socialLinks: SocialLink[];
}

export interface ThemeConfig {
  isDark: boolean;
  primaryColor: string;
  accentColor: string;
}

export interface AnimationConfig {
  duration: number;
  delay: number;
  easing: string;
}

export interface SEOConfig {
  title: string;
  description: string;
  keywords: string[];
  author: string;
  siteUrl: string;
  image?: string;
}
