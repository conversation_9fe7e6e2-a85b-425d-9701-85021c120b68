'use client';

import React, { useEffect, useState, useRef } from 'react';
import { useTheme } from '@/lib/theme-context';

interface IlluminatedElement {
  id: string;
  element: HTMLElement;
  distance: number;
  illumination: number;
}

export default function ContentIllumination() {
  const { theme } = useTheme();
  const [illuminatedElements, setIlluminatedElements] = useState<IlluminatedElement[]>([]);
  const [sunPosition, setSunPosition] = useState({ x: 0, y: 80 });
  const [isThemeTransitioning, setIsThemeTransitioning] = useState(false);
  const [lightingEnabled, setLightingEnabled] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const themeTransitionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isDark = theme === 'dark';

  // Handle theme transitions with proper timing
  useEffect(() => {
    if (themeTransitionTimeoutRef.current) {
      clearTimeout(themeTransitionTimeoutRef.current);
    }

    if (isDark) {
      // Immediately disable lighting when switching to dark mode
      setLightingEnabled(false);
      setIsThemeTransitioning(false);
      document.body.classList.remove('theme-transitioning');
    } else {
      // When switching to light mode, wait for theme transition to complete
      setIsThemeTransitioning(true);
      setLightingEnabled(false);
      document.body.classList.add('theme-transitioning');

      // Wait 1.2 seconds for theme transition to complete before enabling lighting
      themeTransitionTimeoutRef.current = setTimeout(() => {
        setLightingEnabled(true);
        setIsThemeTransitioning(false);
        document.body.classList.remove('theme-transitioning');
      }, 1200);
    }

    return () => {
      if (themeTransitionTimeoutRef.current) {
        clearTimeout(themeTransitionTimeoutRef.current);
      }
    };
  }, [isDark]);

  // Update sun position on mount and resize
  useEffect(() => {
    const updateSunPosition = () => {
      setSunPosition({ x: window.innerWidth - 80, y: 80 });
    };

    updateSunPosition();
    window.addEventListener('resize', updateSunPosition);
    return () => window.removeEventListener('resize', updateSunPosition);
  }, []);

  const calculateDistance = (element: HTMLElement) => {
    const rect = element.getBoundingClientRect();
    const elementCenter = {
      x: rect.left + rect.width / 2,
      y: rect.top + rect.height / 2
    };

    return Math.sqrt(
      Math.pow(elementCenter.x - sunPosition.x, 2) + 
      Math.pow(elementCenter.y - sunPosition.y, 2)
    );
  };

  const calculateIllumination = (distance: number) => {
    const maxDistance = 900; // Increased radius for more coverage
    const minDistance = 80;  // Reduced minimum for stronger center effect

    if (distance <= minDistance) return 1;
    if (distance >= maxDistance) return 0;

    // Enhanced inverse square law for more dramatic falloff
    const normalizedDistance = (distance - minDistance) / (maxDistance - minDistance);
    return Math.max(0, 1 - Math.pow(normalizedDistance, 1.2));
  };

  const applyEnhancedTextIllumination = (element: HTMLElement, illumination: number, intensity: number) => {
    const textElements = element.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, a, li, div');

    textElements.forEach((textEl) => {
      const htmlTextEl = textEl as HTMLElement;

      if (illumination > 0.1) {
        // Dramatically enhanced text lighting effects
        htmlTextEl.style.textShadow = `
          0 0 ${12 * illumination}px rgba(252, 211, 77, ${intensity * 0.9}),
          0 0 ${24 * illumination}px rgba(245, 158, 11, ${intensity * 0.6}),
          0 0 ${36 * illumination}px rgba(255, 193, 7, ${intensity * 0.4}),
          0 1px ${4 * illumination}px rgba(255, 255, 255, ${intensity * 0.8})
        `;

        // Enhanced brightness and warmth
        htmlTextEl.style.filter = `
          brightness(${1 + intensity * 0.8})
          contrast(${1 + intensity * 0.4})
          saturate(${1 + intensity * 0.5})
        `;

        // Warm color tinting
        htmlTextEl.style.color = `color-mix(in srgb, currentColor, #FCD34D ${intensity * 25}%)`;

        // Add warm background glow for headings
        if (htmlTextEl.tagName.match(/^H[1-6]$/)) {
          htmlTextEl.style.background = `linear-gradient(135deg,
            rgba(252, 211, 77, ${intensity * 0.15}) 0%,
            rgba(245, 158, 11, ${intensity * 0.1}) 50%,
            transparent 100%
          )`;
          htmlTextEl.style.backgroundClip = 'text';
          htmlTextEl.style.webkitBackgroundClip = 'text';
        }
      }
    });
  };

  const removeTextIllumination = (element: HTMLElement) => {
    const textElements = element.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, a, li, div');

    textElements.forEach((textEl) => {
      const htmlTextEl = textEl as HTMLElement;
      htmlTextEl.style.textShadow = '';
      htmlTextEl.style.filter = '';
      htmlTextEl.style.color = '';
      htmlTextEl.style.background = '';
      htmlTextEl.style.backgroundClip = '';
      htmlTextEl.style.webkitBackgroundClip = '';
    });
  };

  const updateElementIllumination = () => {
    // Don't apply lighting effects if in dark mode or during theme transition
    if (isDark || !lightingEnabled) {
      // Remove all enhanced lighting effects
      const elements = document.querySelectorAll('[data-illuminatable]');
      elements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        htmlElement.style.transition = 'all 1.2s cubic-bezier(0.4, 0, 0.2, 1)';
        htmlElement.style.boxShadow = '';
        htmlElement.style.backgroundColor = '';
        htmlElement.style.borderColor = '';
        htmlElement.style.filter = '';

        // Remove text illumination effects
        removeTextIllumination(htmlElement);

        htmlElement.classList.remove('sun-illuminated');
        htmlElement.style.removeProperty('--illumination-intensity');
      });
      return;
    }

    const elements = document.querySelectorAll('[data-illuminatable]');
    const updated: IlluminatedElement[] = [];

    elements.forEach((element, index) => {
      const htmlElement = element as HTMLElement;
      const distance = calculateDistance(htmlElement);
      const illumination = calculateIllumination(distance);

      if (illumination > 0.03) { // Lower threshold for more coverage
        updated.push({
          id: `element-${index}`,
          element: htmlElement,
          distance,
          illumination
        });

        // Dramatically increased illumination intensity for realistic lighting
        const intensity = Math.min(illumination * 0.8, 0.4); // Much higher intensity
        htmlElement.style.transition = 'all 1.5s cubic-bezier(0.4, 0, 0.2, 1)';

        // Enhanced lighting effects with much stronger impact
        htmlElement.style.boxShadow = `
          0 0 ${30 * illumination}px rgba(252, 211, 77, ${intensity * 0.9}),
          0 0 ${60 * illumination}px rgba(245, 158, 11, ${intensity * 0.7}),
          0 0 ${90 * illumination}px rgba(255, 193, 7, ${intensity * 0.5}),
          inset 0 0 ${40 * illumination}px rgba(252, 211, 77, ${intensity * 0.3})
        `;

        // Much brighter background illumination
        htmlElement.style.backgroundColor = `rgba(252, 211, 77, ${intensity * 0.25})`;

        // Enhanced brightness and contrast filters
        htmlElement.style.filter = `
          brightness(${1 + intensity * 0.6})
          contrast(${1 + intensity * 0.3})
          saturate(${1 + intensity * 0.4})
        `;

        // Stronger border illumination
        const currentBorderColor = getComputedStyle(htmlElement).borderColor;
        if (currentBorderColor && currentBorderColor !== 'rgba(0, 0, 0, 0)' && currentBorderColor !== 'transparent') {
          htmlElement.style.borderColor = `color-mix(in srgb, ${currentBorderColor}, rgba(252, 211, 77, ${intensity * 0.6}))`;
        }

        // Apply enhanced text illumination
        applyEnhancedTextIllumination(htmlElement, illumination, intensity);

        htmlElement.classList.add('sun-illuminated');
        htmlElement.style.setProperty('--illumination-intensity', illumination.toString());

      } else {
        // Gradually remove all enhanced illumination effects
        htmlElement.style.transition = 'all 2s cubic-bezier(0.4, 0, 0.2, 1)';
        htmlElement.style.boxShadow = '';
        htmlElement.style.backgroundColor = '';
        htmlElement.style.borderColor = '';
        htmlElement.style.filter = '';

        // Remove text illumination effects
        removeTextIllumination(htmlElement);

        htmlElement.classList.remove('sun-illuminated');
        htmlElement.style.removeProperty('--illumination-intensity');
      }
    });

    setIlluminatedElements(updated);
  };

  useEffect(() => {
    if (isDark) {
      // Remove all illumination in dark mode
      const elements = document.querySelectorAll('[data-illuminatable]');
      elements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        htmlElement.style.boxShadow = '';
        htmlElement.style.backgroundColor = '';
        htmlElement.style.borderColor = '';
      });
      return;
    }

    // Set up intersection observer for performance
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.setAttribute('data-illuminatable', 'true');
          } else {
            entry.target.removeAttribute('data-illuminatable');
          }
        });
        updateElementIllumination();
      },
      {
        rootMargin: '100px',
        threshold: 0.1
      }
    );

    // Observe all content elements
    const elementsToObserve = document.querySelectorAll(
      'h1, h2, h3, h4, h5, h6, p, div[class*="card"], section, article, .illuminatable'
    );

    elementsToObserve.forEach((element) => {
      observerRef.current?.observe(element);
    });

    // Update on scroll and resize
    const handleUpdate = () => {
      requestAnimationFrame(updateElementIllumination);
    };

    window.addEventListener('scroll', handleUpdate, { passive: true });
    window.addEventListener('resize', handleUpdate);

    // Initial update
    updateElementIllumination();

    return () => {
      observerRef.current?.disconnect();
      window.removeEventListener('scroll', handleUpdate);
      window.removeEventListener('resize', handleUpdate);
    };
  }, [isDark]);

  // Auto-update illumination periodically for dynamic effect
  useEffect(() => {
    if (isDark) return;

    const interval = setInterval(() => {
      updateElementIllumination();
    }, 2000);

    return () => clearInterval(interval);
  }, [isDark]);

  return null; // This component only applies effects, no visual output
}

// Hook for components to register for illumination
export const useIllumination = () => {
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (element) {
      element.classList.add('illuminatable');
    }

    return () => {
      if (element) {
        element.classList.remove('illuminatable');
        element.style.boxShadow = '';
        element.style.backgroundColor = '';
        element.style.borderColor = '';
      }
    };
  }, []);

  return elementRef;
};
