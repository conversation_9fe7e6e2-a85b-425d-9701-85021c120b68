'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  UserCircle,
  Terminal,
  Sparkles,
  Rocket,
  Send,
  PanelLeftClose,
  PanelLeftOpen,
  Mail,
  Linkedin,
  Github
} from 'lucide-react';

// Alternative icon options available:
// Zap, Home, User, Briefcase, Mail, Globe, Star, Heart, Target, Compass, Layers, MessageCircle, Phone, AtSign

import { useSidebar } from '@/lib/sidebar-context';
import { useTheme } from '@/lib/theme-context';
import { cn } from '@/lib/utils';

// 🎨 ICON SET OPTIONS - Choose your preferred style:

// Terminal Commands Navigation
const navItems = [
  { name: 'cd ~/home', href: '/', icon: Sparkles, command: 'cd ~/home', description: 'Navigate to home directory' },
  { name: 'whoami', href: '/about', icon: UserCircle, command: 'whoami', description: 'Display user information' },
  { name: 'ls projects/', href: '/projects', icon: Rocket, command: 'ls projects/', description: 'List project files' },
  { name: 'ping', href: '/contact', icon: Send, command: 'ping', description: 'Test connection to contact' },
];

// Alternative Option 1: Classic & Professional
// const navItems = [
//   { name: 'Home', href: '/', icon: Home, command: 'cd ~' },
//   { name: 'About', href: '/about', icon: User, command: 'whoami' },
//   { name: 'Projects', href: '/projects', icon: Briefcase, command: 'ls -la' },
//   { name: 'Contact', href: '/contact', icon: Mail, command: 'ping' },
// ];

// Alternative Option 2: Tech & Energetic
// const navItems = [
//   { name: 'Home', href: '/', icon: Zap, command: 'cd ~' },
//   { name: 'About', href: '/about', icon: UserCircle, command: 'whoami' },
//   { name: 'Projects', href: '/projects', icon: Layers, command: 'ls -la' },
//   { name: 'Contact', href: '/contact', icon: MessageCircle, command: 'ping' },
// ];

// Alternative Option 3: Creative & Unique
// const navItems = [
//   { name: 'Home', href: '/', icon: Star, command: 'cd ~' },
//   { name: 'About', href: '/about', icon: Heart, command: 'whoami' },
//   { name: 'Projects', href: '/projects', icon: Target, command: 'ls -la' },
//   { name: 'Contact', href: '/contact', icon: AtSign, command: 'ping' },
// ];

// Alternative Option 4: Navigation & Exploration
// const navItems = [
//   { name: 'Home', href: '/', icon: Compass, command: 'cd ~' },
//   { name: 'About', href: '/about', icon: UserCircle, command: 'whoami' },
//   { name: 'Projects', href: '/projects', icon: Globe, command: 'ls -la' },
//   { name: 'Contact', href: '/contact', icon: Phone, command: 'ping' },
// ];

interface SidebarNavigationProps {
  className?: string;
}

export default function SidebarNavigation({ className }: SidebarNavigationProps) {
  const { isCollapsed, isMobileOpen, setIsMobileOpen, setIsCollapsed, toggleCollapse, toggleMobile } = useSidebar();
  const { theme } = useTheme();

  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const [showKeyboardHint, setShowKeyboardHint] = useState(false);
  const [isHoveringToggle, setIsHoveringToggle] = useState(false);
  const [showCallToAction, setShowCallToAction] = useState(false);
  const [currentCommand, setCurrentCommand] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const pathname = usePathname();

  // Typing animation function
  const typeCommand = async (command: string) => {
    setIsTyping(true);
    setCurrentCommand('');

    for (let i = 0; i <= command.length; i++) {
      setCurrentCommand(command.slice(0, i));
      await new Promise(resolve => setTimeout(resolve, 50)); // Typing speed
    }

    // Add to command history
    setCommandHistory(prev => [...prev.slice(-4), command]); // Keep last 5 commands

    setTimeout(() => {
      setIsTyping(false);
      setCurrentCommand('');
    }, 1000);
  };

  // Handle navigation with typing effect
  const handleNavigation = (item: typeof navItems[0]) => {
    typeCommand(item.command);
    setIsMobileOpen(false);
  };

  // Show keyboard hint on first load
  useEffect(() => {
    const hasSeenHint = localStorage.getItem('sidebar-keyboard-hint-seen');
    if (!hasSeenHint && window.innerWidth >= 768) {
      const timer = setTimeout(() => {
        setShowKeyboardHint(true);
        setTimeout(() => {
          setShowKeyboardHint(false);
          localStorage.setItem('sidebar-keyboard-hint-seen', 'true');
        }, 3000);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, []);

  // Call-to-action animation for toggle button when collapsed
  useEffect(() => {
    if (!isCollapsed || isHoveringToggle) return;

    // Start call-to-action animation after initial page load
    const initialDelay = setTimeout(() => {
      setShowCallToAction(true);

      // Set up periodic animation every 4 seconds
      const interval = setInterval(() => {
        if (!isHoveringToggle && isCollapsed) {
          setShowCallToAction(true);
          setTimeout(() => setShowCallToAction(false), 1500); // Animation lasts 1.5 seconds
        }
      }, 4000);

      return () => clearInterval(interval);
    }, 3000); // Wait 3 seconds after page load

    return () => clearTimeout(initialDelay);
  }, [isCollapsed, isHoveringToggle]);

  // Initialize current page command on load
  useEffect(() => {
    const currentItem = navItems.find(item => item.href === pathname);
    if (currentItem && commandHistory.length === 0) {
      setCommandHistory([currentItem.command]);
    }
  }, [pathname, commandHistory.length]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Close mobile menu on Escape
      if (e.key === 'Escape' && isMobileOpen) {
        setIsMobileOpen(false);
        return;
      }

      // Toggle sidebar on Ctrl/Cmd + B
      if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
        e.preventDefault();
        if (window.innerWidth >= 768) {
          setIsCollapsed(!isCollapsed);
        } else {
          setIsMobileOpen(!isMobileOpen);
        }
        // Show hint briefly when shortcut is used
        setShowKeyboardHint(true);
        setTimeout(() => setShowKeyboardHint(false), 1000);
      }

      // Navigate with arrow keys when sidebar is focused
      if (document.activeElement?.closest('[role="navigation"]')) {
        const navLinks = document.querySelectorAll('[role="menuitem"]');
        const currentIndex = Array.from(navLinks).findIndex(link => link === document.activeElement);

        if (e.key === 'ArrowDown' && currentIndex < navLinks.length - 1) {
          e.preventDefault();
          (navLinks[currentIndex + 1] as HTMLElement).focus();
        } else if (e.key === 'ArrowUp' && currentIndex > 0) {
          e.preventDefault();
          (navLinks[currentIndex - 1] as HTMLElement).focus();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isMobileOpen, isCollapsed]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileOpen]);

  useEffect(() => {
    setIsMobileOpen(false);
  }, [pathname]);

  // Listen for custom event to open mobile sidebar
  useEffect(() => {
    const handleOpenMobileSidebar = () => {
      if (window.innerWidth < 768) {
        setIsMobileOpen(true);
      }
    };

    window.addEventListener('openMobileSidebar', handleOpenMobileSidebar);
    return () => window.removeEventListener('openMobileSidebar', handleOpenMobileSidebar);
  }, []);

  // Touch gesture handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (window.innerWidth < 768) {
      if (isLeftSwipe && isMobileOpen) {
        setIsMobileOpen(false);
      } else if (isRightSwipe && !isMobileOpen) {
        setIsMobileOpen(true);
      }
    }
  };



  const sidebarVariants = {
    expanded: {
      width: 'var(--sidebar-width-expanded)',
      transition: { type: 'spring' as const, damping: 25, stiffness: 120 }
    },
    collapsed: {
      width: 'var(--sidebar-width-collapsed)',
      transition: { type: 'spring' as const, damping: 25, stiffness: 120 }
    }
  };

  const itemVariants = {
    expanded: { opacity: 1, x: 0 },
    collapsed: { opacity: 0, x: -20 }
  };

  const itemTransition = {
    duration: 0.3,
    ease: "easeOut" as const
  };

  return (
    <>
      {/* Skip Link for Accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only fixed top-4 left-4 z-[9999] bg-accent-primary text-white px-4 py-2 rounded-lg font-medium"
      >
        Skip to main content
      </a>

      {/* Enhanced Mobile Toggle Button */}
      <motion.button
        whileHover={{
          scale: 1.1,
          boxShadow: "0 8px 25px rgba(44, 211, 197, 0.4)",
          backgroundColor: "rgba(44, 211, 197, 0.15)"
        }}
        whileTap={{ scale: 0.9 }}
        onClick={toggleMobile}
        className="md:hidden fixed top-4 left-4 z-[var(--z-mobile-toggle)] p-4 rounded-2xl bg-gradient-to-br from-surface-light to-surface-light/80 dark:from-surface-dark dark:to-surface-dark/80 border-2 border-accent-primary/30 shadow-xl transition-all duration-300 backdrop-blur-md group relative overflow-hidden"
        aria-label="Toggle navigation"
        title="Toggle navigation menu"
      >
        {/* Animated Background Glow */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-accent-primary/10 to-accent-primary/5"
          animate={{
            opacity: [0.3, 0.7, 0.3],
            scale: [1, 1.02, 1]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Icon with Enhanced Animation */}
        <motion.div
          animate={{
            rotate: isMobileOpen ? 180 : 0,
            scale: isMobileOpen ? 1.1 : 1
          }}
          transition={{
            duration: 0.4,
            type: "spring",
            stiffness: 200,
            damping: 20
          }}
          className="relative z-10"
        >
          <Terminal
            size={24}
            className="text-accent-primary group-hover:text-accent-primary drop-shadow-sm"
            strokeWidth={2}
          />
        </motion.div>

        {/* Pulse Ring Effect */}
        <motion.div
          className="absolute inset-0 rounded-2xl border-2 border-accent-primary/40"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0, 0.6, 0]
          }}
          transition={{
            duration: 1.8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </motion.button>

      {/* Mobile Overlay */}
      <AnimatePresence>
        {isMobileOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsMobileOpen(false)}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[var(--z-sidebar-overlay)] md:hidden"
            style={{ touchAction: 'none' }}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.aside
        initial={false}
        animate={isCollapsed ? 'collapsed' : 'expanded'}
        variants={sidebarVariants}
        className={cn(
          'fixed left-0 top-0 h-screen w-[var(--sidebar-width-collapsed)] md:w-[var(--sidebar-width-expanded)] z-[var(--z-sidebar)] flex flex-col',
          'md:translate-x-0 transition-all duration-300 ease-in-out will-change-transform',
          'font-mono overflow-hidden',
          // Enhanced styling for collapsed vs expanded
          isCollapsed
            ? 'bg-black bg-opacity-98 border-r-2 border-green-400/30 shadow-2xl shadow-green-400/30'
            : 'bg-black border-r-2 border-gray-600 shadow-2xl shadow-green-400/20',
          // Ensure proper width based on state
          isCollapsed ? 'w-[90px]' : 'w-[220px]',
          isMobileOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0',
          className
        )}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          height: '100vh',
          width: isCollapsed ? '90px' : '220px',
          zIndex: 1035,
          backgroundImage: isCollapsed
            ? 'radial-gradient(circle at 50% 50%, rgba(34, 197, 94, 0.03) 0%, transparent 50%)'
            : undefined
        }}
        role="navigation"
        aria-label="Main navigation"
        aria-expanded={isMobileOpen}
        aria-hidden={!isMobileOpen && (typeof window !== 'undefined' && window.innerWidth < 768)}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Terminal Window Header - Minimal */}
        <div className="bg-gray-800 border-b border-gray-600">
          {/* Terminal Title Bar - Only Window Controls */}
          <div className={cn(
            "flex items-center justify-center bg-gray-700",
            isCollapsed ? "px-2 py-3" : "px-3 py-2"
          )}>
            <AnimatePresence mode="wait">
              {isCollapsed ? (
                <motion.div
                  key="collapsed-controls"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="flex flex-col space-y-1"
                >
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                </motion.div>
              ) : (
                <motion.div
                  key="expanded-controls"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="flex space-x-1"
                >
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Terminal Navigation */}
        <nav className="flex-1 p-4 space-y-1 bg-black font-mono overflow-y-auto" role="menu" aria-label="Terminal navigation">
          {navItems.map((item, index) => {
            const isActive = pathname === item.href;
            const Icon = item.icon;
            
            return (
              <motion.div
                key={item.name}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{
                  delay: index * 0.08,
                  duration: 0.4,
                  ease: "easeOut" as const
                }}
                onHoverStart={() => setHoveredItem(item.name)}
                onHoverEnd={() => setHoveredItem(null)}
              >
                <Link
                  href={item.href}
                  onClick={() => handleNavigation(item)}
                  className={cn(
                    'group relative flex items-center transition-all duration-300 font-mono text-sm',
                    'hover:bg-green-400/10 focus:outline-none focus:ring-2 focus:ring-green-400/50',
                    'rounded border border-transparent px-2 py-1',
                    isActive
                      ? 'bg-green-400/20 border-green-400/30 shadow-lg shadow-green-400/20 text-green-300'
                      : 'text-green-400 hover:text-green-300',
                    // Terminal-style spacing
                    isCollapsed ? 'justify-center' : 'space-x-2'
                  )}
                  role="menuitem"
                  aria-current={isActive ? 'page' : undefined}
                  aria-label={`Execute command: ${item.command}`}
                >
                  {/* Terminal Active Indicator */}
                  {isActive && (
                    <motion.div
                      layoutId="terminalIndicator"
                      className="absolute left-0 top-1/2 -translate-y-1/2 text-green-400"
                      initial={false}
                      transition={{ type: 'spring' as const, bounce: 0.2, duration: 0.6 }}
                    >
                      <span className="text-xs">▶</span>
                    </motion.div>
                  )}

                  {/* Terminal Command Display */}
                  <div className="flex items-center w-full">
                    {isCollapsed ? (
                      // Collapsed: Enhanced icon with terminal styling
                      <motion.div
                        animate={{
                          scale: hoveredItem === item.name ? 1.15 : 1,
                        }}
                        transition={{ duration: 0.2, ease: "easeOut" }}
                        className="flex items-center justify-center w-full relative"
                      >
                        <div className={cn(
                          'relative p-3 rounded-lg transition-all duration-300',
                          isActive
                            ? 'bg-green-400/20 shadow-lg shadow-green-400/30'
                            : 'hover:bg-green-400/10',
                          hoveredItem === item.name && 'bg-green-400/15'
                        )}>
                          <Icon size={24} className={cn(
                            'transition-all duration-300',
                            isActive ? 'text-green-300 drop-shadow-lg' : 'text-green-400',
                            hoveredItem === item.name && 'text-green-300'
                          )} />

                          {/* Terminal glow effect for active state */}
                          {isActive && (
                            <motion.div
                              className="absolute inset-0 rounded-lg border border-green-400/50"
                              animate={{
                                opacity: [0.3, 0.7, 0.3],
                                scale: [1, 1.02, 1]
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut"
                              }}
                            />
                          )}
                        </div>
                      </motion.div>
                    ) : (
                      // Expanded: Clean command text only (no icons, no $ prompt, no cursor)
                      <div className="flex items-center flex-1">
                        <span className={cn(
                          'transition-all duration-300 font-mono text-sm',
                          isActive ? 'text-green-300' : 'text-green-400',
                          hoveredItem === item.name && 'text-green-300'
                        )}>
                          {item.command}
                        </span>
                      </div>
                    )}
                  </div>



                  {/* Terminal-Style Tooltip for Collapsed State */}
                  <AnimatePresence>
                    {isCollapsed && hoveredItem === item.name && (
                      <motion.div
                        initial={{ opacity: 0, x: -10, scale: 0.9 }}
                        animate={{ opacity: 1, x: 0, scale: 1 }}
                        exit={{ opacity: 0, x: -10, scale: 0.9 }}
                        className="absolute left-full ml-3 px-3 py-2 text-sm rounded-md shadow-2xl whitespace-nowrap z-[var(--z-tooltip)] bg-black border border-green-400/50 backdrop-blur-sm"
                        style={{
                          top: '50%',
                          transform: 'translateY(-50%)',
                          boxShadow: '0 0 20px rgba(34, 197, 94, 0.3)'
                        }}
                      >
                        <div className="flex items-center space-x-2 font-mono">
                          <span className="text-green-400">$</span>
                          <span className="text-green-300">{item.command}</span>
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                          {item.description}
                        </div>

                        {/* Terminal-style arrow */}
                        <div
                          className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1 w-2 h-2 bg-black border-l border-t border-green-400/50 rotate-45"
                        />

                        {/* Subtle glow effect */}
                        <motion.div
                          className="absolute inset-0 rounded-md border border-green-400/30 pointer-events-none"
                          animate={{
                            opacity: [0.3, 0.6, 0.3]
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                        />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Link>
              </motion.div>
            );
          })}
        </nav>

        {/* Terminal Prompt Section - Moved below navigation */}
        <div className={cn(
          "bg-black bg-opacity-95 border-t border-gray-600",
          isCollapsed ? "p-2" : "p-3"
        )}>
          <AnimatePresence mode="wait">
            {isCollapsed ? (
              <motion.div
                key="collapsed-prompt"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="flex flex-col items-center space-y-2"
              >
                {/* Minimal prompt */}
                <div className="font-mono text-sm text-green-400 flex items-center">
                  <span className="text-yellow-400">~</span>
                  <span className="text-green-400">$</span>
                  <motion.span
                    animate={{ opacity: [1, 0, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                    className="text-green-400 ml-1"
                  >▋</motion.span>
                </div>

                {/* Terminal activity indicator */}
                {isTyping && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="w-2 h-2 bg-green-400 rounded-full animate-pulse"
                  />
                )}
              </motion.div>
            ) : (
              <motion.div
                key="expanded-prompt"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="font-mono text-xs space-y-1"
              >
                {/* Command History */}
                {commandHistory.map((cmd, index) => (
                  <motion.div
                    key={`${cmd}-${index}`}
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-gray-500"
                  >
                    <span className="text-green-300">user</span>
                    <span className="text-gray-400">@</span>
                    <span className="text-blue-400">portfolio</span>
                    <span className="text-gray-400">:</span>
                    <span className="text-yellow-400">~</span>
                    <span className="text-green-400">$ </span>
                    <span className="text-gray-300">{cmd}</span>
                  </motion.div>
                ))}

                {/* Current Command Line */}
                <div className="text-green-400">
                  <span className="text-green-300">user</span>
                  <span className="text-gray-400">@</span>
                  <span className="text-blue-400">portfolio</span>
                  <span className="text-gray-400">:</span>
                  <span className="text-yellow-400">~</span>
                  <span className="text-green-400">$ </span>
                  <span className="text-gray-300">{currentCommand}</span>
                  <motion.span
                    animate={{ opacity: [1, 0, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                    className="text-green-400"
                  >▋</motion.span>
                </div>

                {!isTyping && commandHistory.length === 0 && (
                  <div className="text-gray-400 text-xs mt-2">
                    Available commands:
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Terminal Footer */}
        <div className="mt-auto bg-black bg-opacity-95"> {/* Terminal footer background */}
          {/* Terminal Separator */}
          <div className={cn(
            "px-4 py-2",
            isCollapsed && "px-2 py-1"
          )}>
            <div className="h-px bg-gradient-to-r from-transparent via-green-400/30 to-transparent"></div>
          </div>

          <div className={cn(
            isCollapsed ? "p-2" : "p-3"
          )}>
            <div className={cn(
              isCollapsed ? "space-y-2" : "space-y-3"
            )}>


            {/* Terminal Social Commands */}
            <div className={cn(
              "flex font-mono text-xs",
              isCollapsed ? "flex-col space-y-2" : "grid grid-cols-2 gap-1"
            )}>
              {[
                { icon: Mail, label: "mail", command: "$ mail", href: "mailto:<EMAIL>" },
                { icon: Linkedin, label: "linkedin", command: "$ linkedin", href: "https://linkedin.com" },
                { icon: Github, label: "github", command: "$ github", href: "https://github.com" }
              ].map((social) => {
                const IconComponent = social.icon;
                return (
                  <motion.a
                    key={social.label}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    whileHover={{ scale: isCollapsed ? 1.1 : 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className={cn(
                      "flex items-center transition-all duration-200 rounded border border-transparent group relative",
                      "hover:bg-green-400/10 hover:border-green-400/30 focus:outline-none focus:ring-2 focus:ring-green-400/50",
                      "text-green-400 hover:text-green-300",
                      isCollapsed
                        ? "justify-center p-2 bg-green-400/5 hover:bg-green-400/15"
                        : "justify-start space-x-1 p-1"
                    )}
                    aria-label={`Execute ${social.command}`}
                  >
                    {!isCollapsed && <span className="text-green-400">$</span>}
                    <IconComponent
                      size={isCollapsed ? 16 : 12}
                      className="transition-colors duration-200"
                    />

                    {/* Enhanced tooltip for collapsed social icons */}
                    {isCollapsed && (
                      <motion.div
                        initial={{ opacity: 0, x: -10, scale: 0.9 }}
                        whileHover={{ opacity: 1, x: 0, scale: 1 }}
                        className="absolute left-full ml-2 px-2 py-1 text-xs rounded bg-black border border-green-400/50 whitespace-nowrap pointer-events-none z-50"
                      >
                        <span className="text-green-400">$ {social.label}</span>
                        <div className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1 w-1 h-1 bg-black border-l border-t border-green-400/50 rotate-45" />
                      </motion.div>
                    )}

                    <AnimatePresence>
                      {!isCollapsed && (
                        <motion.span
                          initial={{ opacity: 0, x: -5 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -5 }}
                          className="transition-colors duration-200"
                        >
                          {social.label}
                        </motion.span>
                      )}
                    </AnimatePresence>
                  </motion.a>
                );
              })}
            </div>



            {/* Terminal System Info */}
            <div className="text-center font-mono text-xs">
              <AnimatePresence mode="wait">
                {isCollapsed ? (
                  <motion.div
                    key="collapsed-info"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    className="flex flex-col items-center space-y-2"
                  >
                    {/* Minimal version info */}
                    <div className="text-gray-500 text-xs">v2.0</div>

                    {/* Terminal status indicator */}
                    <div className="flex items-center space-x-1">
                      <motion.div
                        className="w-1.5 h-1.5 bg-green-400 rounded-full"
                        animate={{
                          opacity: [0.5, 1, 0.5],
                          scale: [1, 1.2, 1]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />
                      <span className="text-green-400 text-xs">ON</span>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    key="expanded-info"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="text-gray-500 space-y-1"
                  >
                    <div className="text-green-400 text-xs">System ready</div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
        </div>

      </motion.aside>

      {/* Enhanced Floating Toggle Icon with Call-to-Action - Desktop Only */}
      <motion.button
        whileHover={{
          scale: 1.2,
        }}
        whileTap={{ scale: 0.9 }}
        onClick={toggleCollapse}
        onHoverStart={() => setIsHoveringToggle(true)}
        onHoverEnd={() => setIsHoveringToggle(false)}
        className="hidden md:flex items-center justify-center transition-all duration-300 group focus:outline-none relative"
        style={{
          position: 'fixed',
          top: 'calc(50vh - 12px)', // Subtract half the icon height (24px / 2 = 12px)
          left: isCollapsed ? '77px' : '207px', // 80px - 16px = 64px, 240px - 16px = 224px
          transformOrigin: 'center center',
          transition: 'left 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          zIndex: 'var(--z-sidebar-toggle)',
          width: '24px',
          height: '24px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
        aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        title={isCollapsed ? 'Expand sidebar (⌘B)' : 'Collapse sidebar (⌘B)'}
      >
        {/* Icon Container with State-Based Animation and Subtle Breathing */}
        <motion.div
          animate={{
            rotate: isCollapsed ? 0 : 180,
            scale: isCollapsed && !isHoveringToggle && !showCallToAction ? [1, 1.05, 1] : 1
          }}
          transition={{
            duration: 0.4,
            type: "spring",
            stiffness: 200,
            damping: 20,
            scale: {
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }
          }}
          className="relative flex items-center justify-center"
        >
          <AnimatePresence mode="wait">
            {isCollapsed ? (
              <motion.div
                key="expand"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.8, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="relative"
              >
                <PanelLeftOpen
                  size={24}
                  className="text-accent-primary group-hover:text-accent-primary drop-shadow-sm"
                  strokeWidth={2.5}
                />
                {/* Collapsed State Indicator */}
                <motion.div
                  className="absolute -top-1 -right-1 w-2 h-2 bg-orange-400 rounded-full"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.7, 1, 0.7]
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              </motion.div>
            ) : (
              <motion.div
                key="collapse"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.8, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="relative"
              >
                <PanelLeftClose
                  size={24}
                  className="text-red-400 group-hover:text-red-400 drop-shadow-sm"
                  strokeWidth={2.5}
                />
                {/* Expanded State Indicator */}
                <motion.div
                  className="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.7, 1, 0.7]
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Call-to-Action Animation Effects */}
        <AnimatePresence>
          {showCallToAction && isCollapsed && (
            <>
              {/* Pulsing Ring Effect */}
              <motion.div
                initial={{ scale: 1, opacity: 0.6 }}
                animate={{
                  scale: [1, 1.8, 1],
                  opacity: [0.6, 0, 0.6]
                }}
                exit={{ scale: 1, opacity: 0 }}
                transition={{
                  duration: 1.5,
                  repeat: 1,
                  ease: "easeOut"
                }}
                className="absolute inset-0 border-2 border-accent-primary rounded-full pointer-events-none"
                style={{
                  width: '32px',
                  height: '32px',
                  left: '-4px',
                  top: '-4px'
                }}
              />

              {/* Glow Effect */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{
                  opacity: [0, 0.4, 0],
                  scale: [0.8, 1.2, 0.8]
                }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{
                  duration: 1.5,
                  repeat: 1,
                  ease: "easeInOut"
                }}
                className="absolute inset-0 bg-accent-primary/20 rounded-full blur-sm pointer-events-none"
                style={{
                  width: '40px',
                  height: '40px',
                  left: '-8px',
                  top: '-8px'
                }}
              />

              {/* Bounce Animation on Icon */}
              <motion.div
                initial={{ y: 0 }}
                animate={{
                  y: [0, -3, 0, -2, 0],
                }}
                transition={{
                  duration: 0.6,
                  delay: 0.3,
                  ease: "easeInOut"
                }}
                className="absolute inset-0 pointer-events-none"
              />
            </>
          )}
        </AnimatePresence>

        {/* Hover Tooltip */}
        <motion.div
          className="absolute -right-4 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-50"
          initial={{ x: -10, opacity: 0 }}
          whileHover={{ x: 0, opacity: 1 }}
        >
          <div className="bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs px-2 py-1 rounded-md whitespace-nowrap shadow-lg">
            {isCollapsed ? 'Expand' : 'Collapse'}
            <div className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-900 dark:bg-gray-100 rotate-45" />
          </div>
        </motion.div>

        {/* Keyboard Shortcut Hint */}
        <AnimatePresence>
          {showKeyboardHint && (
            <motion.div
              initial={{ opacity: 0, y: 10, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.9 }}
              className="absolute -bottom-16 left-1/2 -translate-x-1/2 pointer-events-none z-50"
            >
              <div className="bg-gradient-to-r from-accent-primary to-accent-primary/80 text-white text-xs px-3 py-2 rounded-lg shadow-xl backdrop-blur-sm border border-accent-primary/30">
                <div className="flex items-center space-x-1">
                  <kbd className="bg-white/20 px-1.5 py-0.5 rounded text-xs font-mono">⌘</kbd>
                  <span>+</span>
                  <kbd className="bg-white/20 px-1.5 py-0.5 rounded text-xs font-mono">B</kbd>
                  <span className="ml-1">to toggle</span>
                </div>
                <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1 w-2 h-2 bg-accent-primary rotate-45" />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.button>

      {/* Circuit Board Connection Lines SVG */}
      <AnimatePresence>
        {!isCollapsed && (
          <motion.svg
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.15 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
            className="fixed left-0 top-0 w-[220px] h-full pointer-events-none"
            style={{
              zIndex: 'calc(var(--z-sidebar) - 1)'
            }}
          >
            <defs>
              <linearGradient id="circuitGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#2CD3C5" />
                <stop offset="50%" stopColor="#2CD3C5" />
                <stop offset="100%" stopColor="#24B5A8" />
              </linearGradient>
              <filter id="glow">
                <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
              <filter id="pulseGlow">
                <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>

            {/* Vertical Main Circuit Line */}
            <motion.path
              d="M 70 100 L 70 450"
              stroke="url(#circuitGradient)"
              strokeWidth="2"
              fill="none"
              filter="url(#glow)"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 2, ease: "easeInOut" }}
            />

            {/* Pulsing data flow animation */}
            <motion.circle
              r="3"
              fill="#2CD3C5"
              filter="url(#pulseGlow)"
              animate={{
                cy: [100, 450, 100],
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <animateMotion dur="4s" repeatCount="indefinite">
                <mpath href="#mainCircuitPath" />
              </animateMotion>
            </motion.circle>

            <path id="mainCircuitPath" d="M 70 100 L 70 450" fill="none" opacity="0" />

            {/* Horizontal Connection Lines */}
            {navItems.map((_, index) => {
              const yPos = 150 + index * 70;
              return (
                <g key={index}>
                  <motion.path
                    d={`M 70 ${yPos} L 250 ${yPos}`}
                    stroke="url(#circuitGradient)"
                    strokeWidth="1"
                    strokeDasharray="6 3"
                    fill="none"
                    initial={{ pathLength: 0, opacity: 0 }}
                    animate={{ pathLength: 1, opacity: 1 }}
                    transition={{ delay: index * 0.15 + 0.5, duration: 0.8 }}
                  />

                  {/* Circuit Nodes */}
                  <motion.circle
                    cx="70"
                    cy={yPos}
                    r="2.5"
                    fill="#2CD3C5"
                    filter="url(#glow)"
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: index * 0.15 + 1, duration: 0.4 }}
                  />

                  <motion.circle
                    cx="250"
                    cy={yPos}
                    r="2"
                    fill="#24B5A8"
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: index * 0.15 + 1.2, duration: 0.4 }}
                  />

                  {/* Data pulse animation along horizontal lines */}
                  <motion.circle
                    r="1.5"
                    fill="#2CD3C5"
                    filter="url(#pulseGlow)"
                    animate={{
                      cx: [70, 250, 70],
                      opacity: [0, 1, 0]
                    }}
                    transition={{
                      duration: 3,
                      delay: index * 0.5 + 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    cy={yPos}
                  />
                </g>
              );
            })}
          </motion.svg>
        )}
      </AnimatePresence>

        {/* Geometric Pattern Background */}
        <pattern id="hexPattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
          <polygon
            points="20,5 30,15 30,25 20,35 10,25 10,15"
            fill="none"
            stroke="#2CD3C5"
            strokeWidth="0.5"
            opacity="0.1"
          />
        </pattern>

        <rect width="220" height="100%" fill="url(#hexPattern)" />
      {/* Floating Geometric Particles */}
      <div
        className="fixed left-0 top-0 w-[220px] h-full pointer-events-none overflow-hidden"
        style={{ zIndex: 'calc(var(--z-sidebar) - 2)' }}
      >
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-accent-primary/20 diamond"
            style={{
              left: `${20 + (i * 40)}px`,
              top: `${100 + (i * 60)}px`,
            }}
            animate={{
              y: [-10, 10, -10],
              rotate: [0, 180, 360],
              opacity: [0.2, 0.6, 0.2],
            }}
            transition={{
              duration: 4 + i,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.5,
            }}
          />
        ))}
      </div>
    </>
  );
}
