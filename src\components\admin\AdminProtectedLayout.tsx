'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';

interface AdminProtectedLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
}

export default function AdminProtectedLayout({
  children,
  title = "Admin Panel",
  subtitle
}: AdminProtectedLayoutProps) {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/admin/login');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background-light dark:bg-background-dark flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
          <p className="text-text-secondary-light dark:text-text-secondary-dark font-medium">Loading admin panel...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-background-light dark:bg-background-dark">
      {/* Sidebar */}
      <AdminSidebar user={user} />

      {/* Main Content */}
      <div className="lg:pl-64 relative">
        <div className="flex flex-col min-h-screen">
          {/* Header */}
          <AdminHeader
            title={title}
            subtitle={subtitle}
          />

          {/* Main Content Area */}
          <main className="flex-1 relative z-0">
            <div className="absolute inset-0 bg-gradient-to-br from-primary-50/30 to-transparent dark:from-primary-900/10 pointer-events-none" />
            <div className="relative z-0">
              {children}
            </div>
          </main>

          {/* Admin Footer */}
          <footer className="bg-surface-light dark:bg-surface-dark border-t border-border-light dark:border-border-dark px-6 py-4">
            <div className="flex items-center justify-between text-sm text-text-secondary-light dark:text-text-secondary-dark">
              <div className="flex items-center space-x-4">
                <span>© {new Date().getFullYear()} Admin Panel</span>
                <span className="text-text-muted-light dark:text-text-muted-dark">•</span>
                <span>Portfolio Management System</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs">System Online</span>
              </div>
            </div>
          </footer>
        </div>
      </div>
    </div>
  );
}
