// Performance optimization utilities

export function preloadRoute(href: string) {
  const link = document.createElement('link');
  link.rel = 'prefetch';
  link.href = href;
  document.head.appendChild(link);
}

export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
}

export function lazyLoadImages() {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          img.src = img.dataset.src!;
          img.classList.remove('lazy');
          observer.unobserve(img);
        }
      });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img);
    });
  }
}

export function optimizeBundle() {
  // Dynamic imports for code splitting
  const loadComponent = (componentName: string) => {
    switch (componentName) {
      case 'Button':
        return import('@/components/ui/Button');
      case 'Card':
        return import('@/components/ui/Card');
      default:
        return Promise.reject(new Error(`Component ${componentName} not found`));
    }
  };

  return loadComponent;
}

export function measurePerformance() {
  if ('performance' in window) {
    // Measure Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        console.log(`${entry.name}: ${entry.startTime}ms`);
      });
    });

    observer.observe({ entryTypes: ['measure', 'navigation', 'paint'] });

    // Measure custom metrics
    performance.mark('app-start');
    
    window.addEventListener('load', () => {
      performance.mark('app-loaded');
      performance.measure('app-load-time', 'app-start', 'app-loaded');
    });
  }
}

export function optimizeScrolling() {
  let ticking = false;

  function updateScrollPosition() {
    // Update scroll-dependent elements
    const scrollY = window.pageYOffset;
    document.documentElement.style.setProperty('--scroll-y', `${scrollY}px`);
    ticking = false;
  }

  function requestTick() {
    if (!ticking) {
      requestAnimationFrame(updateScrollPosition);
      ticking = true;
    }
  }

  window.addEventListener('scroll', requestTick, { passive: true });
}

export function enableServiceWorker() {
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration);
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError);
        });
    });
  }
}

export function optimizeImages() {
  // Add loading="lazy" to images below the fold
  const images = document.querySelectorAll('img');
  images.forEach((img, index) => {
    if (index > 2) { // Skip first 3 images (above the fold)
      img.loading = 'lazy';
    }
  });
}

export function prefetchCriticalResources() {
  const criticalResources = [
    '/fonts/inter.woff2',
    '/fonts/jetbrains-mono.woff2',
    '/fonts/space-grotesk.woff2',
  ];

  criticalResources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource;
    link.as = 'font';
    link.type = 'font/woff2';
    link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
  });
}

export function initializePerformanceOptimizations() {
  if (typeof window !== 'undefined') {
    measurePerformance();
    optimizeScrolling();
    lazyLoadImages();
    optimizeImages();
    
    // Prefetch critical resources
    document.addEventListener('DOMContentLoaded', () => {
      prefetchCriticalResources();
    });

    // Preload routes on hover
    document.addEventListener('mouseover', (e) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a[href^="/"]') as HTMLAnchorElement;
      if (link && !link.dataset.prefetched) {
        preloadRoute(link.href);
        link.dataset.prefetched = 'true';
      }
    });
  }
}

// Web Vitals measurement
export function measureWebVitals() {
  if (typeof window !== 'undefined') {
    // Largest Contentful Paint
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      const lastEntry = entries[entries.length - 1];
      console.log('LCP:', lastEntry.startTime);
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach((entry) => {
        console.log('FID:', entry.processingStart - entry.startTime);
      });
    }).observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift
    let clsValue = 0;
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      console.log('CLS:', clsValue);
    }).observe({ entryTypes: ['layout-shift'] });
  }
}
