// Performance monitoring utilities

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  type: 'timing' | 'counter' | 'gauge';
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];
  private isEnabled: boolean = false;

  constructor() {
    this.isEnabled = typeof window !== 'undefined' && 'performance' in window;
    if (this.isEnabled) {
      this.initializeObservers();
    }
  }

  private initializeObservers() {
    try {
      // Core Web Vitals observer
      const vitalsObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          this.recordMetric({
            name: entry.name,
            value: entry.startTime,
            timestamp: Date.now(),
            type: 'timing'
          });
        });
      });

      vitalsObserver.observe({ 
        entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] 
      });
      this.observers.push(vitalsObserver);

      // Navigation timing observer
      const navigationObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            this.recordMetric({
              name: 'page-load-time',
              value: navEntry.loadEventEnd - navEntry.navigationStart,
              timestamp: Date.now(),
              type: 'timing'
            });
            this.recordMetric({
              name: 'dom-content-loaded',
              value: navEntry.domContentLoadedEventEnd - navEntry.navigationStart,
              timestamp: Date.now(),
              type: 'timing'
            });
          }
        });
      });

      navigationObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navigationObserver);

      // Resource timing observer for API calls
      const resourceObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.name.includes('/api/')) {
            this.recordMetric({
              name: `api-call-${entry.name.split('/api/')[1]}`,
              value: entry.duration,
              timestamp: Date.now(),
              type: 'timing'
            });
          }
        });
      });

      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);

    } catch (error) {
      console.warn('Performance monitoring setup failed:', error);
    }
  }

  recordMetric(metric: PerformanceMetric) {
    this.metrics.push(metric);
    
    // Keep only last 100 metrics to prevent memory leaks
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }

    // Log significant performance issues
    if (metric.type === 'timing' && metric.value > 3000) {
      console.warn(`Slow performance detected: ${metric.name} took ${metric.value}ms`);
    }
  }

  // Track API call frequency
  trackApiCall(endpoint: string) {
    this.recordMetric({
      name: `api-call-count-${endpoint}`,
      value: 1,
      timestamp: Date.now(),
      type: 'counter'
    });
  }

  // Track component render times
  trackComponentRender(componentName: string, renderTime: number) {
    this.recordMetric({
      name: `component-render-${componentName}`,
      value: renderTime,
      timestamp: Date.now(),
      type: 'timing'
    });
  }

  // Get performance summary
  getPerformanceSummary() {
    if (!this.isEnabled) {
      return { message: 'Performance monitoring not available' };
    }

    const now = Date.now();
    const recentMetrics = this.metrics.filter(m => now - m.timestamp < 60000); // Last minute

    const apiCalls = recentMetrics.filter(m => m.name.includes('api-call'));
    const renderTimes = recentMetrics.filter(m => m.name.includes('component-render'));
    
    return {
      totalMetrics: this.metrics.length,
      recentApiCalls: apiCalls.length,
      averageApiTime: apiCalls.length > 0 
        ? apiCalls.reduce((sum, m) => sum + m.value, 0) / apiCalls.length 
        : 0,
      slowestComponent: renderTimes.length > 0 
        ? renderTimes.reduce((slowest, current) => 
            current.value > slowest.value ? current : slowest
          )
        : null,
      metrics: recentMetrics
    };
  }

  // Check for performance issues
  checkPerformanceIssues() {
    const issues: string[] = [];
    const now = Date.now();
    const recentMetrics = this.metrics.filter(m => now - m.timestamp < 30000); // Last 30 seconds

    // Check for excessive API calls
    const apiCalls = recentMetrics.filter(m => m.name.includes('api-call-count'));
    const portfolioApiCalls = apiCalls.filter(m => m.name.includes('portfolio'));
    
    if (portfolioApiCalls.length > 5) {
      issues.push(`Excessive API calls detected: ${portfolioApiCalls.length} calls to portfolio API in 30 seconds`);
    }

    // Check for slow renders
    const slowRenders = recentMetrics.filter(m => 
      m.name.includes('component-render') && m.value > 100
    );
    
    if (slowRenders.length > 0) {
      issues.push(`Slow component renders detected: ${slowRenders.length} components took >100ms`);
    }

    return issues;
  }

  // Clean up observers
  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics = [];
  }
}

// Global instance
let performanceMonitor: PerformanceMonitor | null = null;

export function getPerformanceMonitor(): PerformanceMonitor {
  if (!performanceMonitor) {
    performanceMonitor = new PerformanceMonitor();
  }
  return performanceMonitor;
}

// React hook for performance monitoring
export function usePerformanceMonitor() {
  const monitor = getPerformanceMonitor();

  const trackRender = (componentName: string) => {
    const startTime = performance.now();
    return () => {
      const endTime = performance.now();
      monitor.trackComponentRender(componentName, endTime - startTime);
    };
  };

  const trackApiCall = (endpoint: string) => {
    monitor.trackApiCall(endpoint);
  };

  const getIssues = () => {
    return monitor.checkPerformanceIssues();
  };

  const getSummary = () => {
    return monitor.getPerformanceSummary();
  };

  return {
    trackRender,
    trackApiCall,
    getIssues,
    getSummary
  };
}

// Development helper to log performance issues
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  setInterval(() => {
    const monitor = getPerformanceMonitor();
    const issues = monitor.checkPerformanceIssues();
    
    if (issues.length > 0) {
      console.group('🚨 Performance Issues Detected');
      issues.forEach(issue => console.warn(issue));
      console.groupEnd();
    }
  }, 10000); // Check every 10 seconds
}
